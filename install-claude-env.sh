#!/bin/bash

# =====================================
# Claude Code 环境自动化安装脚本
# 适用于 WSL Ubuntu
# =====================================

set -e

echo "==== Claude Code 环境自动化安装开始 ===="

# 1. 安装 Node.js 18
if ! command -v node >/dev/null 2>&1; then
  echo "[1/4] 安装 Node.js 18..."
  curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
  sudo apt-get install -y nodejs
else
  echo "[1/4] Node.js 已安装，跳过。"
fi

echo "Node.js 版本: $(node --version)"
echo "npm 版本: $(npm --version)"

# 2. 安装 Claude Code
if ! npm list -g @anthropic-ai/claude-code >/dev/null 2>&1; then
  echo "[2/4] 安装 Claude Code..."
  sudo npm install -g @anthropic-ai/claude-code
else
  echo "[2/4] Claude Code 已安装，跳过。"
fi

# 3. 配置代理
read -p "请输入你的 Windows 局域网 IP（如************）: " WINIP
read -p "请输入 Clash 代理端口（如7890）: " PORT

if ! grep -q "http_proxy" ~/.bashrc; then
  echo "[3/4] 写入 http/https 代理到 ~/.bashrc..."
  echo "export http_proxy=\"http://$WINIP:$PORT\"" >> ~/.bashrc
  echo "export https_proxy=\"http://$WINIP:$PORT\"" >> ~/.bashrc
else
  echo "[3/4] ~/.bashrc 已有代理配置，跳过。"
fi

# 4. 配置 Claude API KEY
read -p "请输入你的 Claude API KEY: " CLAUDE_API_KEY
if ! grep -q "CLAUDE_API_KEY" ~/.bashrc; then
  echo "[4/4] 写入 CLAUDE_API_KEY 到 ~/.bashrc..."
  echo "export CLAUDE_API_KEY=\"$CLAUDE_API_KEY\"" >> ~/.bashrc
else
  echo "[4/4] ~/.bashrc 已有 CLAUDE_API_KEY，跳过。"
fi

source ~/.bashrc

echo "==== 安装完成！===="
echo "请将 start-claude.sh、启动ClaudeCode.bat、ClaudeCode使用说明.md 拷贝到本机项目目录。"
echo "你可以直接用 bash start-claude.sh 或双击启动ClaudeCode.bat 一键启动 Claude Code。"
