@echo off
chcp 65001 >nul
echo ========================================
echo         Claude Code 一键启动器
echo ========================================
echo.

REM 默认工程目录（可修改为你的常用项目路径）
set "DEFAULT_PROJECT=/mnt/e/BaiduSyncdisk/01BLWK_PROJETR/01cubeMX/05Camera_RF/Camera_CAT1测试版1/Core"

REM 判断是否传入了参数（项目路径）
if "%1"=="" (
    set "PROJECT_PATH=%DEFAULT_PROJECT%"
) else (
    set "PROJECT_PATH=%1"
)

echo 正在启动 WSL Ubuntu 并进入项目目录...
echo 项目目录: %PROJECT_PATH%
echo.

REM 调用 WSL 执行启动脚本
wsl -d Ubuntu -e bash -c "cd /mnt/e/BaiduSyncdisk/01BLWK_PROJETR/04Cursor/Augnent_rest && bash start-claude.sh '%PROJECT_PATH%'"

echo.
echo Claude Code 已退出
pause
