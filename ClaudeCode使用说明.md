# Claude Code 终端工具使用说明

## 1. 启动方式

### 推荐脚本启动

在 WSL 终端中，进入脚本所在目录，执行：

```bash
bash start-claude.sh
```

- 默认会自动进入你常用的工程目录：
  `/mnt/e/BaiduSyncdisk/01BLWK_PROJETR/01cubeMX/05Camera_RF/Camera_CAT1测试版1/Core`
- 如果要打开其他项目，可以传入路径参数：

```bash
bash start-claude.sh /mnt/e/你的/其他/项目路径
```

---

## 2. 如何打开项目文件夹

- 脚本会自动 `cd` 到你指定的项目目录。
- 你也可以手动进入项目目录后再执行 `claude` 命令：

```bash
cd /mnt/e/BaiduSyncdisk/01BLWK_PROJETR/01cubeMX/05Camera_RF/Camera_CAT1测试版1/Core
export CLAUDE_API_KEY="你的API密钥"
export http_proxy="http://你的IP:7890"
export https_proxy="http://你的IP:7890"
claude
```

---

## 3. <PERSON> Code 常用命令

- 启动 Claude Code：
  ```bash
  claude
  ```
- 指定项目目录启动：
  ```bash
  claude /mnt/e/你的/项目路径
  ```
- 指定模型：
  ```bash
  claude --model claude-3-5-sonnet-20241022
  ```
- 查看帮助：
  ```bash
  claude --help
  ```

---

## 4. 典型用法举例

### 代码生成
> 直接用中文或英文描述你要的功能

```
请帮我写一个C语言的I2C驱动初始化函数
```

### 代码解释
```
请解释这段代码的作用：
[粘贴你的代码]
```

### 代码重构/优化
```
请优化下面的代码，让它更高效：
[粘贴你的代码]
```

### 调试与修复
```
帮我找出这段代码的bug并修复：
[粘贴你的代码]
```

### 文档生成
```
请为下面的函数生成详细的中文注释
[粘贴你的代码]
```

---

## 5. 高级技巧

- 支持多轮对话，Claude 会记住上下文。
- 可以直接让 Claude 解释、生成、重构、补全、写文档等。
- 支持多种编程语言（C/C++/Python/JS/Java/Go/嵌入式等）。
- 支持直接编辑和保存项目文件。

---

## 6. 常见问题

- **代理无效/网络不通**：请确保 Clash 允许局域网，WSL 代理变量已设置。
- **API KEY 报错**：请确认密钥有效且有配额。
- **项目目录不存在**：请检查路径拼写，或用绝对路径。

---

## 7. 参考链接
- [Claude Code 官方文档](https://docs.anthropic.com/en/docs/claude-code/overview)
- [Anthropic 支持页面](https://support.anthropic.com/)

---

如有更多问题，欢迎随时提问！
