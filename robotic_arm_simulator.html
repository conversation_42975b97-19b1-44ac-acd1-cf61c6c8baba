<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>4轴机械臂模拟器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f0f0;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .control-panel {
            width: 300px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        .control-group {
            margin-bottom: 25px;
        }

        .control-group h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: 600;
        }

        .slider-container {
            margin-bottom: 15px;
        }

        .slider-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            color: #555;
            font-size: 14px;
        }

        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
            transition: background 0.3s;
        }

        .slider::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4CAF50;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .slider::-webkit-slider-thumb:hover {
            background: #45a049;
            transform: scale(1.1);
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4CAF50;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 80px;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        .btn-toggle {
            background: #17a2b8;
            color: white;
        }

        .btn-toggle:hover {
            background: #138496;
            transform: translateY(-1px);
        }

        .canvas-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }
        
        .canvas-container canvas {
            width: 100% !important;
            height: 100% !important;
        }

        #robotCanvas {
            display: block;
            cursor: grab;
        }

        #robotCanvas:active {
            cursor: grabbing;
        }

        .info-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            min-width: 200px;
        }

        .info-panel h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 12px;
            color: #666;
        }

        .joint-angles {
            margin-bottom: 15px;
        }

        .end-effector-pos {
            border-top: 1px solid #eee;
            padding-top: 10px;
        }

        .instructions {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            max-width: 300px;
        }

        .instructions h4 {
            margin-bottom: 8px;
            color: #4CAF50;
        }

        .instructions ul {
            padding-left: 15px;
        }

        .instructions li {
            margin-bottom: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="control-panel">
            <div class="control-group">
                <h3>🦾 关节控制</h3>
                
                <div class="slider-container">
                    <div class="slider-label">
                        <span>基座旋转</span>
                        <span id="joint1Value">0°</span>
                    </div>
                    <input type="range" id="joint1" class="slider" min="-180" max="180" value="0" step="1">
                </div>

                <div class="slider-container">
                    <div class="slider-label">
                        <span>肩部关节</span>
                        <span id="joint2Value">45°</span>
                    </div>
                    <input type="range" id="joint2" class="slider" min="-90" max="90" value="45" step="1">
                </div>

                <div class="slider-container">
                    <div class="slider-label">
                        <span>肘部关节</span>
                        <span id="joint3Value">-90°</span>
                    </div>
                    <input type="range" id="joint3" class="slider" min="-135" max="135" value="-90" step="1">
                </div>

                <div class="slider-container">
                    <div class="slider-label">
                        <span>腕部关节</span>
                        <span id="joint4Value">0°</span>
                    </div>
                    <input type="range" id="joint4" class="slider" min="-180" max="180" value="0" step="1">
                </div>
            </div>

            <div class="control-group">
                <h3>🎮 快速动作</h3>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="resetToHome()">复位</button>
                    <button class="btn btn-secondary" onclick="randomPose()">随机姿态</button>
                </div>
                <div class="button-group" style="margin-top: 10px;">
                    <button class="btn btn-primary" onclick="presetPose1()">预设1</button>
                    <button class="btn btn-primary" onclick="presetPose2()">预设2</button>
                    <button class="btn btn-primary" onclick="presetPose3()">预设3</button>
                </div>
            </div>

            <div class="control-group">
                <h3>🎨 视觉设置</h3>
                <div class="button-group">
                    <button class="btn btn-toggle" onclick="toggleBackground()">切换背景</button>
                    <button class="btn btn-toggle" onclick="toggleTrajectory()">轨迹显示</button>
                </div>
                <div class="button-group" style="margin-top: 10px;">
                    <button class="btn btn-secondary" onclick="clearTrajectory()">清除轨迹</button>
                </div>
            </div>

            <div class="control-group">
                <h3>📹 视角控制</h3>
                <div class="button-group">
                    <button class="btn btn-secondary" onclick="resetCamera()">重置视角</button>
                    <button class="btn btn-secondary" onclick="topView()">俯视图</button>
                </div>
                <div class="button-group" style="margin-top: 10px;">
                    <button class="btn btn-secondary" onclick="frontView()">正视图</button>
                    <button class="btn btn-secondary" onclick="sideView()">侧视图</button>
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="robotCanvas"></canvas>
            
            <div class="info-panel">
                <h4>📊 实时信息</h4>
                <div class="joint-angles">
                    <div class="info-item">
                        <span>基座:</span>
                        <span id="infoJoint1">0°</span>
                    </div>
                    <div class="info-item">
                        <span>肩部:</span>
                        <span id="infoJoint2">45°</span>
                    </div>
                    <div class="info-item">
                        <span>肘部:</span>
                        <span id="infoJoint3">-90°</span>
                    </div>
                    <div class="info-item">
                        <span>腕部:</span>
                        <span id="infoJoint4">0°</span>
                    </div>
                </div>
                <div class="end-effector-pos">
                    <div class="info-item">
                        <span>末端X:</span>
                        <span id="infoEndX">0.0</span>
                    </div>
                    <div class="info-item">
                        <span>末端Y:</span>
                        <span id="infoEndY">0.0</span>
                    </div>
                    <div class="info-item">
                        <span>末端Z:</span>
                        <span id="infoEndZ">0.0</span>
                    </div>
                </div>
            </div>

            <div class="instructions">
                <h4>💡 操作说明</h4>
                <ul>
                    <li>拖拽鼠标旋转视角</li>
                    <li>右键拖拽平移场景</li>
                    <li>滚轮缩放画面</li>
                    <li>滑条控制关节角度</li>
                    <li>绿色轨迹为末端运动路径</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // 全局变量
        let scene, camera, renderer, robot = {};
        let cameraControls = {
            mouseX: 0, mouseY: 0,
            isMouseDown: false,
            isRightMouseDown: false,
            radius: 12,
            theta: Math.PI / 4,
            phi: Math.PI / 3,
            target: { x: 0, y: 4, z: 0 },
            panSpeed: 0.01
        };
        
        let trajectoryPoints = [];
        let trajectoryLine = null;
        let showTrajectory = true;
        let backgroundWhite = true;
        
        // 机械臂参数
        const armParams = {
            baseHeight: 1,
            link1Length: 3,
            link2Length: 3,
            link3Length: 2,
            // 递减的半径参数
            baseRadius: 0.25,      // 基座连杆最粗
            link1RadiusTop: 0.2,   // 第一段臂顶部
            link1RadiusBottom: 0.25, // 第一段臂底部
            link2RadiusTop: 0.15,  // 第二段臂顶部
            link2RadiusBottom: 0.2, // 第二段臂底部
            link3RadiusTop: 0.1,   // 第三段臂顶部
            link3RadiusBottom: 0.15, // 第三段臂底部
            jointRadius: 0.25,     // 关节半径
            endEffectorRadius: 0.08 // 末端执行器半径
        };

        // 关节角度（弧度）
        let jointAngles = {
            base: 0,
            shoulder: Math.PI / 4,
            elbow: -Math.PI / 2,
            wrist: 0
        };

        // 初始化场景
        function initScene() {
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0xffffff);

            // 创建相机
            const canvas = document.getElementById('robotCanvas');
            camera = new THREE.PerspectiveCamera(60, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ 
                canvas: canvas, 
                antialias: true,
                alpha: true 
            });
            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            renderer.setClearColor(0xffffff, 1);

            // 添加灯光
            setupLighting();
            
            // 创建机械臂
            createRobotArm();
            
            // 创建地面
            createGround();
            
            // 设置相机位置
            updateCameraPosition();
            
            // 添加事件监听器
            setupEventListeners();
            
            // 开始渲染循环
            animate();
        }

        // 设置灯光
        function setupLighting() {
            // 环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);

            // 主光源
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 20, 10);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            directionalLight.shadow.camera.near = 0.5;
            directionalLight.shadow.camera.far = 50;
            directionalLight.shadow.camera.left = -15;
            directionalLight.shadow.camera.right = 15;
            directionalLight.shadow.camera.top = 15;
            directionalLight.shadow.camera.bottom = -15;
            scene.add(directionalLight);

            // 补光
            const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
            fillLight.position.set(-10, 10, -10);
            scene.add(fillLight);
        }

        // 创建机械臂
        function createRobotArm() {
            robot.group = new THREE.Group();
            scene.add(robot.group);

            // 材质定义
            const baseMaterial = new THREE.MeshPhongMaterial({ color: 0x2c3e50 });
            const linkMaterial = new THREE.MeshPhongMaterial({ color: 0x3498db });
            const jointMaterial = new THREE.MeshPhongMaterial({ color: 0xe74c3c });
            const endEffectorMaterial = new THREE.MeshPhongMaterial({ color: 0xf39c12 });

            // 基座
            const baseGeometry = new THREE.CylinderGeometry(1, 1.2, armParams.baseHeight, 12);
            robot.base = new THREE.Mesh(baseGeometry, baseMaterial);
            robot.base.position.y = armParams.baseHeight / 2;
            robot.base.castShadow = true;
            robot.base.receiveShadow = true;
            robot.group.add(robot.base);

            // 基座关节（旋转平台）
            robot.baseJoint = new THREE.Group();
            robot.baseJoint.position.y = armParams.baseHeight;
            robot.base.add(robot.baseJoint);

            // 第一段臂（肩部到肘部）- 从粗到细的锥形
            const link1Geometry = new THREE.CylinderGeometry(armParams.link1RadiusTop, armParams.link1RadiusBottom, armParams.link1Length, 12);
            robot.link1 = new THREE.Mesh(link1Geometry, linkMaterial);
            robot.link1.position.y = armParams.link1Length / 2;
            robot.link1.castShadow = true;
            robot.baseJoint.add(robot.link1);

            // 肩部关节外壳
            const shoulderJointGeometry = new THREE.SphereGeometry(armParams.jointRadius, 16, 12);
            robot.shoulderJoint = new THREE.Mesh(shoulderJointGeometry, jointMaterial);
            robot.shoulderJoint.castShadow = true;
            robot.baseJoint.add(robot.shoulderJoint);

            // 肘部关节组
            robot.elbowJoint = new THREE.Group();
            robot.elbowJoint.position.y = armParams.link1Length;
            robot.baseJoint.add(robot.elbowJoint);

            // 肘部关节外壳
            const elbowJointGeometry = new THREE.SphereGeometry(armParams.jointRadius * 0.9, 16, 12);
            const elbowJointMesh = new THREE.Mesh(elbowJointGeometry, jointMaterial);
            elbowJointMesh.castShadow = true;
            robot.elbowJoint.add(elbowJointMesh);

            // 第二段臂（肘部到腕部）- 从粗到细的锥形
            const link2Geometry = new THREE.CylinderGeometry(armParams.link2RadiusTop, armParams.link2RadiusBottom, armParams.link2Length, 12);
            robot.link2 = new THREE.Mesh(link2Geometry, linkMaterial);
            robot.link2.position.y = armParams.link2Length / 2;
            robot.link2.castShadow = true;
            robot.elbowJoint.add(robot.link2);

            // 腕部关节组
            robot.wristJoint = new THREE.Group();
            robot.wristJoint.position.y = armParams.link2Length;
            robot.elbowJoint.add(robot.wristJoint);

            // 腕部关节外壳
            const wristJointGeometry = new THREE.SphereGeometry(armParams.jointRadius * 0.7, 16, 12);
            const wristJointMesh = new THREE.Mesh(wristJointGeometry, jointMaterial);
            wristJointMesh.castShadow = true;
            robot.wristJoint.add(wristJointMesh);

            // 第三段臂（腕部到末端执行器）- 从粗到细的锥形
            const link3Geometry = new THREE.CylinderGeometry(armParams.link3RadiusTop, armParams.link3RadiusBottom, armParams.link3Length, 12);
            robot.link3 = new THREE.Mesh(link3Geometry, linkMaterial);
            robot.link3.position.y = armParams.link3Length / 2;
            robot.link3.castShadow = true;
            robot.wristJoint.add(robot.link3);

            // 末端执行器
            robot.endEffectorJoint = new THREE.Group();
            robot.endEffectorJoint.position.y = armParams.link3Length;
            robot.wristJoint.add(robot.endEffectorJoint);

            // 末端执行器基座
            const endEffectorBaseGeometry = new THREE.CylinderGeometry(armParams.endEffectorRadius, armParams.endEffectorRadius * 1.2, 0.3, 12);
            robot.endEffectorBase = new THREE.Mesh(endEffectorBaseGeometry, endEffectorMaterial);
            robot.endEffectorBase.position.y = 0.15;
            robot.endEffectorBase.castShadow = true;
            robot.endEffectorJoint.add(robot.endEffectorBase);

            // 夹爪连接器
            const gripperConnectorGeometry = new THREE.CylinderGeometry(0.06, 0.08, 0.2, 8);
            const gripperConnector = new THREE.Mesh(gripperConnectorGeometry, endEffectorMaterial);
            gripperConnector.position.y = 0.4;
            gripperConnector.castShadow = true;
            robot.endEffectorJoint.add(gripperConnector);

            // 创建更逼真的夹爪
            const gripperMaterial = new THREE.MeshPhongMaterial({ color: 0x95a5a6 });
            
            // 左侧夹爪组件
            robot.leftGripper = new THREE.Group();
            robot.leftGripper.position.set(0.12, 0.5, 0);
            
            // 左夹爪主体
            const leftMainGeometry = new THREE.BoxGeometry(0.08, 0.25, 0.04);
            const leftMain = new THREE.Mesh(leftMainGeometry, gripperMaterial);
            leftMain.castShadow = true;
            robot.leftGripper.add(leftMain);
            
            // 左夹爪指尖
            const leftTipGeometry = new THREE.BoxGeometry(0.06, 0.15, 0.02);
            const leftTip = new THREE.Mesh(leftTipGeometry, gripperMaterial);
            leftTip.position.set(0, 0.2, 0);
            leftTip.castShadow = true;
            robot.leftGripper.add(leftTip);
            
            // 左夹爪底部加强筋
            const leftReinforceGeometry = new THREE.BoxGeometry(0.04, 0.1, 0.06);
            const leftReinforce = new THREE.Mesh(leftReinforceGeometry, gripperMaterial);
            leftReinforce.position.set(-0.02, -0.15, 0);
            leftReinforce.castShadow = true;
            robot.leftGripper.add(leftReinforce);
            
            robot.endEffectorJoint.add(robot.leftGripper);

            // 右侧夹爪组件（镜像左侧）
            robot.rightGripper = new THREE.Group();
            robot.rightGripper.position.set(-0.12, 0.5, 0);
            
            // 右夹爪主体
            const rightMainGeometry = new THREE.BoxGeometry(0.08, 0.25, 0.04);
            const rightMain = new THREE.Mesh(rightMainGeometry, gripperMaterial);
            rightMain.castShadow = true;
            robot.rightGripper.add(rightMain);
            
            // 右夹爪指尖
            const rightTipGeometry = new THREE.BoxGeometry(0.06, 0.15, 0.02);
            const rightTip = new THREE.Mesh(rightTipGeometry, gripperMaterial);
            rightTip.position.set(0, 0.2, 0);
            rightTip.castShadow = true;
            robot.rightGripper.add(rightTip);
            
            // 右夹爪底部加强筋
            const rightReinforceGeometry = new THREE.BoxGeometry(0.04, 0.1, 0.06);
            const rightReinforce = new THREE.Mesh(rightReinforceGeometry, gripperMaterial);
            rightReinforce.position.set(0.02, -0.15, 0);
            rightReinforce.castShadow = true;
            robot.rightGripper.add(rightReinforce);
            
            robot.endEffectorJoint.add(robot.rightGripper);
            
            // 设置末端执行器为夹爪连接器，用于位置计算
            robot.endEffector = gripperConnector;

            // 更新机械臂姿态
            updateRobotPose();
        }

        // 创建地面
        function createGround() {
            const groundGeometry = new THREE.PlaneGeometry(30, 30);
            const groundMaterial = new THREE.MeshPhongMaterial({ 
                color: backgroundWhite ? 0xf8f9fa : 0x2c3e50,
                transparent: true,
                opacity: 0.8
            });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);
            robot.ground = ground;

            // 添加网格
            const gridHelper = new THREE.GridHelper(20, 20, 0x888888, 0xcccccc);
            gridHelper.material.transparent = true;
            gridHelper.material.opacity = 0.3;
            scene.add(gridHelper);
            robot.grid = gridHelper;
        }

        // 更新机械臂姿态
        function updateRobotPose() {
            // 基座旋转
            robot.baseJoint.rotation.y = jointAngles.base;
            
            // 肩部关节旋转
            robot.elbowJoint.rotation.z = jointAngles.shoulder;
            robot.shoulderJoint.rotation.z = jointAngles.shoulder;
            
            // 肘部关节旋转
            robot.wristJoint.rotation.z = jointAngles.elbow;
            
            // 腕部关节旋转
            robot.endEffectorJoint.rotation.z = jointAngles.wrist;

            // 计算末端执行器世界坐标
            updateEndEffectorPosition();
            
            // 更新信息面板
            updateInfoPanel();
        }

        // 计算末端执行器位置
        function updateEndEffectorPosition() {
            // 创建临时向量来计算末端执行器的世界坐标
            const endEffectorWorldPos = new THREE.Vector3();
            robot.endEffector.getWorldPosition(endEffectorWorldPos);
            
            // 更新轨迹
            if (showTrajectory) {
                addTrajectoryPoint(endEffectorWorldPos);
            }
            
            return endEffectorWorldPos;
        }

        // 添加轨迹点
        function addTrajectoryPoint(position) {
            trajectoryPoints.push(position.clone());
            
            // 限制轨迹点数量
            if (trajectoryPoints.length > 300) {
                trajectoryPoints.shift();
            }
            
            updateTrajectoryLine();
        }

        // 更新轨迹线
        function updateTrajectoryLine() {
            if (trajectoryLine) {
                scene.remove(trajectoryLine);
            }
            
            if (trajectoryPoints.length > 1 && showTrajectory) {
                const geometry = new THREE.BufferGeometry().setFromPoints(trajectoryPoints);
                const material = new THREE.LineBasicMaterial({
                    color: 0x00ff00,
                    linewidth: 2,
                    transparent: true,
                    opacity: 0.8
                });
                trajectoryLine = new THREE.Line(geometry, material);
                scene.add(trajectoryLine);
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            const canvas = document.getElementById('robotCanvas');
            
            // 鼠标事件
            canvas.addEventListener('mousedown', onMouseDown);
            canvas.addEventListener('mousemove', onMouseMove);
            canvas.addEventListener('mouseup', onMouseUp);
            canvas.addEventListener('wheel', onMouseWheel);
            canvas.addEventListener('contextmenu', (e) => e.preventDefault());
            
            // 窗口大小调整
            window.addEventListener('resize', onWindowResize);
            
            // 滑条事件
            document.getElementById('joint1').addEventListener('input', updateJoint1);
            document.getElementById('joint2').addEventListener('input', updateJoint2);
            document.getElementById('joint3').addEventListener('input', updateJoint3);
            document.getElementById('joint4').addEventListener('input', updateJoint4);
        }

        // 鼠标按下事件
        function onMouseDown(event) {
            if (event.button === 0) { // 左键
                cameraControls.isMouseDown = true;
            } else if (event.button === 2) { // 右键
                cameraControls.isRightMouseDown = true;
            }
            cameraControls.mouseX = event.clientX;
            cameraControls.mouseY = event.clientY;
        }

        // 鼠标移动事件
        function onMouseMove(event) {
            if (cameraControls.isMouseDown) {
                // 左键拖拽 - 旋转视角
                const deltaX = event.clientX - cameraControls.mouseX;
                const deltaY = event.clientY - cameraControls.mouseY;
                
                cameraControls.theta -= deltaX * 0.01;
                cameraControls.phi += deltaY * 0.01;
                cameraControls.phi = Math.max(0.1, Math.min(Math.PI - 0.1, cameraControls.phi));
                
                updateCameraPosition();
            } else if (cameraControls.isRightMouseDown) {
                // 右键拖拽 - 平移场景
                const deltaX = event.clientX - cameraControls.mouseX;
                const deltaY = event.clientY - cameraControls.mouseY;
                
                const right = new THREE.Vector3();
                const up = new THREE.Vector3();
                camera.getWorldDirection(new THREE.Vector3());
                right.setFromMatrixColumn(camera.matrixWorld, 0);
                up.setFromMatrixColumn(camera.matrixWorld, 1);
                
                cameraControls.target.x -= right.x * deltaX * cameraControls.panSpeed;
                cameraControls.target.y += up.y * deltaY * cameraControls.panSpeed;
                cameraControls.target.z -= right.z * deltaX * cameraControls.panSpeed;
                
                updateCameraPosition();
            }
            
            cameraControls.mouseX = event.clientX;
            cameraControls.mouseY = event.clientY;
        }

        // 鼠标释放事件
        function onMouseUp(event) {
            cameraControls.isMouseDown = false;
            cameraControls.isRightMouseDown = false;
        }

        // 鼠标滚轮事件
        function onMouseWheel(event) {
            event.preventDefault();
            cameraControls.radius += event.deltaY * 0.01;
            cameraControls.radius = Math.max(5, Math.min(30, cameraControls.radius));
            updateCameraPosition();
        }

        // 更新相机位置
        function updateCameraPosition() {
            const x = cameraControls.target.x + cameraControls.radius * Math.sin(cameraControls.phi) * Math.cos(cameraControls.theta);
            const y = cameraControls.target.y + cameraControls.radius * Math.cos(cameraControls.phi);
            const z = cameraControls.target.z + cameraControls.radius * Math.sin(cameraControls.phi) * Math.sin(cameraControls.theta);
            
            camera.position.set(x, y, z);
            camera.lookAt(cameraControls.target.x, cameraControls.target.y, cameraControls.target.z);
        }

        // 关节控制函数
        function updateJoint1(event) {
            const value = parseInt(event.target.value);
            jointAngles.base = value * Math.PI / 180;
            document.getElementById('joint1Value').textContent = value + '°';
            updateRobotPose();
        }

        function updateJoint2(event) {
            const value = parseInt(event.target.value);
            jointAngles.shoulder = value * Math.PI / 180;
            document.getElementById('joint2Value').textContent = value + '°';
            updateRobotPose();
        }

        function updateJoint3(event) {
            const value = parseInt(event.target.value);
            jointAngles.elbow = value * Math.PI / 180;
            document.getElementById('joint3Value').textContent = value + '°';
            updateRobotPose();
        }

        function updateJoint4(event) {
            const value = parseInt(event.target.value);
            jointAngles.wrist = value * Math.PI / 180;
            document.getElementById('joint4Value').textContent = value + '°';
            updateRobotPose();
        }

        // 更新信息面板
        function updateInfoPanel() {
            document.getElementById('infoJoint1').textContent = Math.round(jointAngles.base * 180 / Math.PI) + '°';
            document.getElementById('infoJoint2').textContent = Math.round(jointAngles.shoulder * 180 / Math.PI) + '°';
            document.getElementById('infoJoint3').textContent = Math.round(jointAngles.elbow * 180 / Math.PI) + '°';
            document.getElementById('infoJoint4').textContent = Math.round(jointAngles.wrist * 180 / Math.PI) + '°';
            
            // 直接计算末端执行器位置，不调用updateEndEffectorPosition
            const endEffectorWorldPos = new THREE.Vector3();
            if (robot.endEffector) {
                robot.endEffector.getWorldPosition(endEffectorWorldPos);
                document.getElementById('infoEndX').textContent = endEffectorWorldPos.x.toFixed(1);
                document.getElementById('infoEndY').textContent = endEffectorWorldPos.y.toFixed(1);
                document.getElementById('infoEndZ').textContent = endEffectorWorldPos.z.toFixed(1);
            }
        }

        // 控制按钮函数
        function resetToHome() {
            setJointAngles(0, 45, -90, 0);
        }

        function randomPose() {
            const randomAngle = () => Math.random() * 360 - 180;
            setJointAngles(
                randomAngle(),
                Math.random() * 180 - 90,
                Math.random() * 270 - 135,
                randomAngle()
            );
        }

        function presetPose1() {
            setJointAngles(90, 60, -120, 45);
        }

        function presetPose2() {
            setJointAngles(-45, 30, -60, -90);
        }

        function presetPose3() {
            setJointAngles(135, -30, 90, 180);
        }

        function setJointAngles(j1, j2, j3, j4) {
            document.getElementById('joint1').value = j1;
            document.getElementById('joint2').value = j2;
            document.getElementById('joint3').value = j3;
            document.getElementById('joint4').value = j4;
            
            jointAngles.base = j1 * Math.PI / 180;
            jointAngles.shoulder = j2 * Math.PI / 180;
            jointAngles.elbow = j3 * Math.PI / 180;
            jointAngles.wrist = j4 * Math.PI / 180;
            
            document.getElementById('joint1Value').textContent = j1 + '°';
            document.getElementById('joint2Value').textContent = j2 + '°';
            document.getElementById('joint3Value').textContent = j3 + '°';
            document.getElementById('joint4Value').textContent = j4 + '°';
            
            updateRobotPose();
        }

        function toggleBackground() {
            backgroundWhite = !backgroundWhite;
            scene.background = new THREE.Color(backgroundWhite ? 0xffffff : 0x333333);
            
            if (robot.ground) {
                robot.ground.material.color.setHex(backgroundWhite ? 0xf8f9fa : 0x2c3e50);
            }
        }

        function toggleTrajectory() {
            showTrajectory = !showTrajectory;
            if (!showTrajectory && trajectoryLine) {
                scene.remove(trajectoryLine);
                trajectoryLine = null;
            }
        }

        function clearTrajectory() {
            trajectoryPoints = [];
            if (trajectoryLine) {
                scene.remove(trajectoryLine);
                trajectoryLine = null;
            }
        }

        function resetCamera() {
            cameraControls.radius = 12;
            cameraControls.theta = Math.PI / 4;
            cameraControls.phi = Math.PI / 3;
            cameraControls.target = { x: 0, y: 4, z: 0 };
            updateCameraPosition();
        }

        function topView() {
            cameraControls.phi = 0.1;
            cameraControls.theta = 0;
            updateCameraPosition();
        }

        function frontView() {
            cameraControls.phi = Math.PI / 2;
            cameraControls.theta = 0;
            updateCameraPosition();
        }

        function sideView() {
            cameraControls.phi = Math.PI / 2;
            cameraControls.theta = Math.PI / 2;
            updateCameraPosition();
        }

        // 窗口大小调整
        function onWindowResize() {
            const canvas = document.getElementById('robotCanvas');
            camera.aspect = canvas.clientWidth / canvas.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        }

        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);
            
            // 自动淡化轨迹
            if (trajectoryLine && trajectoryPoints.length > 50) {
                if (Math.random() < 0.02) { // 2% 概率移除最旧的点
                    trajectoryPoints.shift();
                    updateTrajectoryLine();
                }
            }
            
            renderer.render(scene, camera);
        }

        // 初始化应用
        window.addEventListener('load', () => {
            initScene();
        });
    </script>
</body>
</html>