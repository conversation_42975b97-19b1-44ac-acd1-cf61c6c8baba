#!/bin/bash

# ========== 用户可配置部分 ==========
# Claude API 密钥
export CLAUDE_API_KEY="************************************************************************************************************"
# 代理设置（如有需要请修改为你的实际IP和端口）
export http_proxy="http://************:7890"
export https_proxy="http://************:7890"
# 默认工程目录（可修改为你的常用项目路径）
DEFAULT_PROJECT="/home/<USER>/vscode_pj/Cloude_code"
# ========== 用户可配置部分结束 ==========

# 判断是否传入了参数（项目路径）
if [ -n "$1" ]; then
  PROJECT_PATH="$1"
else
  PROJECT_PATH="$DEFAULT_PROJECT"
fi

# 切换到项目目录
cd "$PROJECT_PATH" || { echo "项目目录不存在: $PROJECT_PATH"; exit 1; }

# 显示环境信息
clear
echo "====================================="
echo " Claude Code 启动脚本"
echo "====================================="
echo "API 密钥已设置: ${CLAUDE_API_KEY:0:20}..."
echo "代理: $http_proxy"
echo "项目目录: $(pwd)"
echo "Node.js 版本: $(node --version)"
echo "npm 版本: $(npm --version)"
echo "====================================="

# 启动 Claude Code
echo "正在启动 Claude Code..."
claude
