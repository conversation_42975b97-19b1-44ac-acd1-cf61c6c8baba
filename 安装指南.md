# Claude Code 环境安装指南（Windows + WSL）

本指南适用于在新电脑上快速搭建 Claude Code 终端开发环境。

---

## 1. 安装 WSL 和 Ubuntu

1. 打开 Windows PowerShell（以管理员身份）。
2. 执行：
   ```powershell
   wsl --install -d Ubuntu
   ```
3. 重启电脑，首次打开 Ubuntu，设置用户名和密码。

---

## 2. 自动化一键安装（推荐）

1. 将 `install-claude-env.sh` 脚本拷贝到 WSL Ubuntu 的任意目录。
2. 在 WSL 终端运行：
   ```bash
   bash install-claude-env.sh
   ```
3. 按提示输入 Windows 局域网 IP、Clash 端口、Claude API KEY
4. 安装完成后，将 `start-claude.sh`、`启动ClaudeCode.bat`、`ClaudeCode使用说明.md` 拷贝到你的项目目录。
5. 直接用 `bash start-claude.sh` 或双击 `启动ClaudeCode.bat` 一键启动 Claude Code。

---

## 3. 手动安装流程（如需自定义）

### 3.1 安装 Node.js（在 WSL Ubuntu 里）

```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 3.2 安装 Claude Code

```bash
sudo npm install -g @anthropic-ai/claude-code
```

### 3.3 配置 Clash 代理（如需科学上网）

1. 打开 Clash for Windows
2. 开启“允许局域网”
3. 记下 HTTP 代理端口（如 7890）
4. 用 `ipconfig` 查找 Windows 局域网 IP（如 192.168.x.x）

### 3.4 配置 WSL 代理环境变量

在 WSL 终端执行（建议写入 `~/.bashrc`）：

```bash
echo 'export http_proxy="http://你的Windows局域网IP:7890"' >> ~/.bashrc
echo 'export https_proxy="http://你的Windows局域网IP:7890"' >> ~/.bashrc
source ~/.bashrc
```

### 3.5 配置 Claude API KEY

- 推荐在 `start-claude.sh` 脚本中直接写入
- 或者写入 `~/.bashrc`：

```bash
echo 'export CLAUDE_API_KEY="你的API密钥"' >> ~/.bashrc
source ~/.bashrc
```

### 3.6 拷贝启动脚本和说明文件

将以下文件放到你的项目目录：
- `start-claude.sh`（WSL 启动脚本）
- `启动ClaudeCode.bat`（Windows 一键启动脚本）
- `ClaudeCode使用说明.md`（使用说明）

---

## 4. 一键启动

- 直接双击 `启动ClaudeCode.bat`
- 或在 WSL 终端运行：
  ```bash
  bash start-claude.sh
  ```

---

## 5. 常见问题

- **代理无效/网络不通**：请确保 Clash 允许局域网，WSL 代理变量已设置。
- **API KEY 报错**：请确认密钥有效且有配额。
- **项目目录不存在**：请检查路径拼写，或用绝对路径。
- **终端进入 OAuth 登录界面**：说明 API KEY 没生效，请检查环境变量。

---

## 6. 参考链接
- [Claude Code 官方文档](https://docs.anthropic.com/en/docs/claude-code/overview)
- [Anthropic 支持页面](https://support.anthropic.com/)

---

如需自动化安装脚本、批量部署或遇到其他问题，欢迎随时提问！


